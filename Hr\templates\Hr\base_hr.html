{% load static %}
{% load i18n %}
{% load image_utils %}
<!DOCTYPE html>
<html lang="{{ current_language }}" dir="{{ text_direction }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الموارد البشرية{% endblock %}</title>

    <!-- Bootstrap RTL/LTR -->
    {% if text_direction == 'rtl' %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style_updated.css' %}">

    <style>        :root {
            --font-family: {{ current_font|default:'Cairo' }}, sans-serif;
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --success-color: #2ecc71;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #3498db;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --body-bg: #f8f9fa;
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --border-radius: 12px;
            --transition: all 0.3s ease;
        }

        html, body {
            height: 100%;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--body-bg);
            overflow-x: hidden;
        }

        /* Layout */
        .page-wrapper {
            display: flex;
            flex-direction: column;
            min-height: 100%;
        }

        .content-wrapper {
            flex: 1;
            display: flex;
        }        /* Sidebar */
        .sidebar {
            width: 250px;
            background-color: var(--secondary-color);
            color: white;
            height: 100vh;
            position: fixed;
            right: 0;
            top: 0;
            z-index: 1000;
            transition: var(--transition);
            box-shadow: -3px 0 10px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
            padding-top: 60px; /* Space for navbar */
        }

        /* Collapsed sidebar - only show icons */
        .sidebar-collapsed .sidebar {
            width: 70px;
        }

        .sidebar-collapsed .sidebar-header h3,
        .sidebar-collapsed .sidebar-menu-item span {
            display: none;
        }

        /* Adjust content margin when sidebar is collapsed */
        .sidebar-collapsed .main-content {
            margin-right: 70px;
        }

        /* Mobile view */
        @media (max-width: 992px) {
            .sidebar.collapsed {
                right: -280px;
            }
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.15);
        }

        .sidebar-header h3 {
            color: #fff;
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 0;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 1rem 0;
        }        .sidebar-menu-item {
            padding: 10px 20px;
            cursor: pointer;
            border-radius: 8px;
            display: flex;
            align-items: center;
            transition: var(--transition);
            margin: 5px 10px;
        }

        .sidebar-menu-item:hover,
        .sidebar-menu-item.active {
            background-color: var(--primary-color);
            transform: translateX(-5px);
        }

        .sidebar-menu-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            display: flex;
            align-items: center;
            width: 100%;
            font-weight: 500;
        }

        .sidebar-menu-item:hover a,
        .sidebar-menu-item.active a {
            color: white;
        }

        .sidebar-menu-item i {
            margin-left: 10px;
            width: 22px;
            text-align: center;
        }

        .sidebar-menu-item a {
            color: white;
            text-decoration: none;
            display: block;
            width: 100%;
        }

        /* Main Content */        .main-content {
            flex: 1;
            margin-right: 250px;
            transition: var(--transition);
            padding: 70px 30px 30px 30px;
            background-color: var(--body-bg);
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-right: 0;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Navbar */        .navbar {
            background-color: var(--primary-color);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1020;
            padding: 0.5rem 1rem;
            height: 60px;
            transition: var(--transition);
        }

        .navbar-brand {
            color: white !important;
            font-weight: bold;
        }

        .navbar-light .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.85);
        }

        .navbar-light .navbar-nav .nav-link:hover {
            color: white;
        }

        .navbar.with-sidebar {
            padding-right: 295px;
        }

        .navbar.sidebar-collapsed {
            padding-right: 15px;
        }

        /* Toggle Button */        #sidebarToggle {
            background-color: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
        }

        #sidebarToggle:hover {
            background-color: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        /* Responsive */
        @media (max-width: 992px) {
            .sidebar {
                right: -280px;
                z-index: 1050;
            }

            .sidebar.collapsed {
                right: 0;
            }

            .main-content {
                margin-right: 0;
            }

            .navbar {
                padding-right: 15px !important;
            }

            /* Overlay for mobile sidebar */
            .sidebar-overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1040;
            }

            .sidebar-overlay.show {
                display: block;
            }
        }        /* Cards */
        .card {
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            border: none;
            margin-bottom: 1.5rem;
            transition: var(--transition);
            background: #fff;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            background: linear-gradient(45deg, var(--primary-color), var(--info-color));
            color: white;
            padding: 1.25rem;
            border: none;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .card-header h5 {
            color: white !important;
            margin: 0;
            font-weight: 600;
        }

        .card-header i {
            margin-left: 10px;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            font-size: 1rem;
        }        /* Alerts */
        .alerts-container {
            margin-bottom: 1.5rem;
        }

        .alert {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--card-shadow);
            padding: 1rem 1.25rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateY(-20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .alert-success {
            background-color: rgba(46, 204, 113, 0.1);
            color: var(--success-color);
        }

        .alert-danger {
            background-color: rgba(231, 76, 60, 0.1);
            color: var(--danger-color);
        }

        .alert-warning {
            background-color: rgba(243, 156, 18, 0.1);
            color: var(--warning-color);
        }

        .alert-info {
            background-color: rgba(52, 152, 219, 0.1);
            color: var(--info-color);
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="page-wrapper">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg navbar-light with-sidebar">
            <div class="container-fluid">
                <div class="d-flex align-items-center">
                    <button id="sidebarToggle" class="btn btn-primary me-2">
                        <i class="fas fa-bars"></i>
                    </button>
                    <a class="navbar-brand" href="{% url 'accounts:home' %}">
                        <i class="fas fa-users me-2"></i>
                        <span>نظام الموارد البشرية</span>
                    </a>
                </div>
                <div class="d-flex align-items-center">                    <div class="user-info me-3 d-flex align-items-center">
                        <i class="fas fa-user-circle text-light me-2 fs-5"></i>
                        <span class="text-light fw-bold">{{ request.user.username }}</span>
                    </div>
                    <a href="{% url 'accounts:logout' %}" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i>
                        <span class="d-none d-md-inline-block">تسجيل الخروج</span>
                    </a>
                </div>
            </div>
        </nav>

        <div class="content-wrapper">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <h3><i class="fas fa-users me-2"></i> الموارد البشرية</h3>
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="{% url 'Hr:dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'Hr:employees:list' %}">
                            <i class="fas fa-user"></i>
                            <span>الموظفين</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'Hr:departments:list' %}">
                            <i class="fas fa-building"></i>
                            <span>الأقسام</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'Hr:jobs:list' %}">
                            <i class="fas fa-briefcase"></i>
                            <span>الوظائف</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'Hr:salary_item_list' %}">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>الرواتب</span>
                        </a>
                    </li>                    <li class="sidebar-menu-item">
                        <a href="{% url 'attendance:dashboard' %}">
                            <i class="fas fa-clock"></i>
                            <span>نظام الحضور والانصراف</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'attendance:record_list' %}">
                            <i class="fas fa-clipboard-check"></i>
                            <span>سجل الحضور والانصراف</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">                        <a href="{% url 'attendance:mark_attendance' %}">
                            <i class="fas fa-fingerprint"></i>
                            <span>تسجيل الحضور</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'attendance:leave_balance_list' %}">
                            <i class="fas fa-calendar-alt"></i>
                            <span>رصيد الإجازات</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'Hr:leave_types:list' %}">
                            <i class="fas fa-list-alt"></i>
                            <span>أنواع الإجازات</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'Hr:leaves:list' %}">
                            <i class="fas fa-calendar-check"></i>
                            <span>إجازات الموظفين</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'Hr:leave_analytics' %}">
                            <i class="fas fa-chart-pie"></i>
                            <span>تحليل الإجازات</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'Hr:tasks:list' %}">
                            <i class="fas fa-tasks"></i>
                            <span>المهام</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'Hr:reports:list' %}">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'Hr:org_chart:view' %}">
                            <i class="fas fa-sitemap"></i>
                            <span>الهيكل التنظيمي</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'accounts:home' %}">
                            <i class="fas fa-home"></i>
                            <span>الصفحة الرئيسية</span>
                        </a>
                    </li>
                </ul>
            </aside>

            <!-- Main Content -->
            <div class="main-content">                <!-- Page Header -->
                <div class="page-header mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1 fw-bold text-dark">{% block page_title %}الموارد البشرية{% endblock %}</h2>
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb mb-0">
                                    {% block breadcrumb %}
                                    <li class="breadcrumb-item"><a href="{% url 'accounts:home' %}" class="text-primary">الرئيسية</a></li>
                                    <li class="breadcrumb-item active text-muted">الموارد البشرية</li>
                                    {% endblock %}
                                </ol>
                            </nav>
                        </div>
                        <div class="page-actions">
                            {% block page_actions %}{% endblock %}
                        </div>
                    </div>
                </div>

                <!-- Alerts -->
                {% if messages %}
                <div class="alerts-container">
                    {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Main Content -->
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');
            const navbar = document.querySelector('.navbar');
            const sidebarToggle = document.getElementById('sidebarToggle');

            // Create overlay element for mobile
            const overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            document.body.appendChild(overlay);

            // Toggle sidebar function
            function toggleSidebar() {
                // For desktop view - collapse/expand sidebar
                document.body.classList.toggle('sidebar-collapsed');

                // For mobile view
                if (window.innerWidth < 992) {
                    sidebar.classList.toggle('collapsed');
                    mainContent.classList.toggle('expanded');
                    navbar.classList.toggle('with-sidebar');
                    navbar.classList.toggle('sidebar-collapsed');
                    overlay.classList.toggle('show');
                }

                // Save state to localStorage
                const sidebarState = (window.innerWidth < 992 && sidebar.classList.contains('collapsed')) ||
                                   (window.innerWidth >= 992 && document.body.classList.contains('sidebar-collapsed'))
                                   ? 'collapsed' : 'expanded';
                localStorage.setItem('hrSidebarState', sidebarState);
            }

            // Initialize sidebar state from localStorage
            function initSidebarState() {
                const savedState = localStorage.getItem('hrSidebarState');

                // Default to expanded on desktop, collapsed on mobile
                const defaultState = window.innerWidth < 992 ? 'collapsed' : 'expanded';
                const sidebarState = savedState || defaultState;

                if (sidebarState === 'collapsed') {
                    if (window.innerWidth < 992) {
                        // Mobile view - hide sidebar
                        sidebar.classList.add('collapsed');
                        mainContent.classList.add('expanded');
                        navbar.classList.remove('with-sidebar');
                        navbar.classList.add('sidebar-collapsed');
                    } else {
                        // Desktop view - collapse sidebar to icons only
                        document.body.classList.add('sidebar-collapsed');
                    }
                } else {
                    if (window.innerWidth < 992) {
                        // Mobile view - show sidebar
                        sidebar.classList.remove('collapsed');
                        mainContent.classList.remove('expanded');
                        navbar.classList.add('with-sidebar');
                        navbar.classList.remove('sidebar-collapsed');

                        // Show overlay if sidebar is expanded on mobile
                        overlay.classList.add('show');
                    } else {
                        // Desktop view - expand sidebar
                        document.body.classList.remove('sidebar-collapsed');
                    }
                }
            }

            // Toggle sidebar on button click
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', toggleSidebar);
            }

            // Close sidebar when clicking on overlay
            overlay.addEventListener('click', function() {
                if (sidebar.classList.contains('collapsed') === false) {
                    toggleSidebar();
                }
            });

            // Initialize sidebar state
            initSidebarState();

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth < 992) {
                    // On mobile, collapse sidebar by default
                    if (!sidebar.classList.contains('collapsed')) {
                        sidebar.classList.add('collapsed');
                        mainContent.classList.add('expanded');
                        navbar.classList.remove('with-sidebar');
                        navbar.classList.add('sidebar-collapsed');
                        overlay.classList.remove('show');
                    }
                } else {
                    // Remove overlay on desktop
                    overlay.classList.remove('show');
                }
            });

            // Highlight active menu item
            const currentPath = window.location.pathname;
            const menuItems = document.querySelectorAll('.sidebar-menu-item');

            menuItems.forEach(item => {
                const link = item.querySelector('a');
                if (link && currentPath.includes(link.getAttribute('href'))) {
                    item.classList.add('active');
                }
            });
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
