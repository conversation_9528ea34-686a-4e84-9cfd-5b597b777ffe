{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام الشركة الدولية إنترناشونال{% endblock %}</title>
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style_updated.css' %}">
    {% block extra_css %}{% endblock %}
</head>
<body data-is-superuser="{{ request.user.is_superuser|yesno:'true,false' }}">
    <!-- Main Navigation -->
    <nav class="navbar navbar-expand-lg sticky-top">
        <div class="container">
            <a class="navbar-brand" href="{% url 'accounts:home' %}">
                <i class="fas fa-building me-2"></i>
                <span>نظام الشركة الدولية إنترناشونال</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <!-- Empty navbar - links now shown in sidebar based on user permissions -->
                </ul>
                <div class="d-flex">
                    <!-- Notifications Button -->
                    <div class="me-3">
                        <a href="{% url 'notifications:user_notifications' %}" class="btn btn-outline-primary btn-sm position-relative">
                            <i class="fas fa-bell"></i>
                            {% if unread_notifications_count > 0 %}
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                {{ unread_notifications_count }}
                                <span class="visually-hidden">تنبيهات غير مقروءة</span>
                            </span>
                            {% endif %}
                        </a>
                    </div>

                    <!-- User Dropdown -->
                    <div class="dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i> {{ request.user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{% url 'accounts:dashboard' %}"><i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم</a></li>
                            <li><a class="dropdown-item" href="{% url 'notifications:user_notifications' %}"><i class="fas fa-bell me-2"></i> تنبيهاتي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:logout' %}"><i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <h1 class="page-title">{% block page_title %}{% endblock %}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'accounts:home' %}"><i class="fas fa-home"></i></a></li>
                    {% block breadcrumb %}{% endblock %}
                </ol>
            </nav>
        </div>
    </div>

    <!-- Messages/Alerts -->
    <div class="container mt-3">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    <i class="fas {% if message.tags == 'success' %}fa-check-circle{% elif message.tags == 'error' %}fa-exclamation-circle{% elif message.tags == 'warning' %}fa-exclamation-triangle{% else %}fa-info-circle{% endif %} me-2"></i>
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container py-4">
            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>نظام الدولية انترناشونال للطباعة والمنتجات الصحية</h5>
                    <p>نظام إدارة الاقسام والمهام للشركة الدولية</p>
                    <p>Mohamed Gamal</p>

                </div>
                <div class="col-md-6 text-md-end">
                    <div class="footer-links">
                        <a href="#"><i class="fas fa-shield-alt me-1"></i> سياسة الخصوصية</a>
                        <span class="mx-2">|</span>
                        <a href="#"><i class="fas fa-file-contract me-1"></i> شروط الاستخدام</a>
                    </div>
                    <p class="mt-2">&copy; {{ current_year|default:"2025" }} نظام الدولية . جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scroll to top button -->
    <button id="scrollToTop" title="العودة للأعلى">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
